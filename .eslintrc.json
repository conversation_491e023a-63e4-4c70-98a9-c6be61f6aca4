{"extends": ["next/core-web-vitals", "plugin:react/recommended", "google", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "ignorePatterns": ["src/components/ui"], "rules": {"require-jsdoc": ["error", {"require": {"FunctionDeclaration": false, "MethodDefinition": false, "ClassDeclaration": false, "ArrowFunctionExpression": false, "FunctionExpression": false}}], "no-console": ["error", {"allow": ["warn", "error"]}], "react/react-in-jsx-scope": "off", "react/jsx-uses-react": "off"}, "settings": {"react": {"version": "detect"}}}