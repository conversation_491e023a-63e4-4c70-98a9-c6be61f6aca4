import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { z } from "zod";

// Validation schema for forgot password request
const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

/**
 * POST /api/password/forgot-password
 * Handles password reset token generation for users
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = forgotPasswordSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Invalid email address provided",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Check if JWT_SECRET is configured
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;
    if (!jwtSecret) {
      console.error("JWT_SECRET or NEXTAUTH_SECRET not configured");
      return NextResponse.json(
        {
          message: "Server configuration error. Please contact support.",
        },
        { status: 500 }
      );
    }

    // For now, we'll generate a token without checking if user exists
    // In a real implementation, you would verify the user exists in your database
    // and send an email with the reset link
    
    // Generate password reset token (expires in 1 hour)
    const resetToken = jwt.sign(
      {
        email: email,
        type: "password_reset",
        timestamp: Date.now(),
      },
      jwtSecret,
      {
        expiresIn: "1h",
        issuer: "careerireland",
        audience: "password-reset",
      }
    );

    // In a real implementation, you would:
    // 1. Check if user with this email exists
    // 2. Store the reset token in database with expiration
    // 3. Send email with reset link containing the token
    // 4. Return success message without exposing whether user exists

    // For development/testing purposes, we'll return the token
    // In production, this should only return a success message
    const isDevelopment = process.env.NODE_ENV === "development";

    if (isDevelopment) {
      console.log(`Password reset token for ${email}:`, resetToken);
      console.log(`Reset URL: ${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`);
    }

    return NextResponse.json(
      {
        message: "If an account with this email exists, you will receive a password reset link shortly.",
        ...(isDevelopment && { 
          token: resetToken,
          resetUrl: `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`
        }),
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Forgot password error:", error);
    
    // Handle JWT signing errors specifically
    if (error instanceof Error && error.message.includes("secretOrPrivateKey")) {
      return NextResponse.json(
        {
          message: "Server configuration error. Please contact support.",
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: "An unexpected error occurred. Please try again later.",
      },
      { status: 500 }
    );
  }
}
