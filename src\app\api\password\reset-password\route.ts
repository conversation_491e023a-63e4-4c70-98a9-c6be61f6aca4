import { NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { z } from "zod";

// Validation schema for reset password request
const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

/**
 * POST /api/password/reset-password
 * Handles password reset using a valid reset token
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = resetPasswordSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Invalid request data",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { token, password } = validationResult.data;

    // Check if JWT_SECRET is configured
    const jwtSecret = process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET;
    if (!jwtSecret) {
      console.error("JWT_SECRET or NEXTAUTH_SECRET not configured");
      return NextResponse.json(
        {
          message: "Server configuration error. Please contact support.",
        },
        { status: 500 }
      );
    }

    // Verify and decode the reset token
    let decodedToken: any;
    try {
      decodedToken = jwt.verify(token, jwtSecret, {
        issuer: "careerireland",
        audience: "password-reset",
      });
    } catch (jwtError) {
      console.error("JWT verification error:", jwtError);
      
      if (jwtError instanceof jwt.TokenExpiredError) {
        return NextResponse.json(
          {
            message: "Password reset token has expired. Please request a new one.",
          },
          { status: 400 }
        );
      }
      
      if (jwtError instanceof jwt.JsonWebTokenError) {
        return NextResponse.json(
          {
            message: "Invalid password reset token. Please request a new one.",
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          message: "Invalid password reset token. Please request a new one.",
        },
        { status: 400 }
      );
    }

    // Validate token payload
    if (!decodedToken.email || decodedToken.type !== "password_reset") {
      return NextResponse.json(
        {
          message: "Invalid password reset token. Please request a new one.",
        },
        { status: 400 }
      );
    }

    // In a real implementation, you would:
    // 1. Check if the token exists in your database and hasn't been used
    // 2. Verify the user exists
    // 3. Hash the new password
    // 4. Update the user's password in the database
    // 5. Invalidate the reset token
    // 6. Optionally send confirmation email

    // For development/testing purposes, we'll just log the operation
    const isDevelopment = process.env.NODE_ENV === "development";
    
    if (isDevelopment) {
      console.log(`Password reset successful for email: ${decodedToken.email}`);
      console.log(`New password length: ${password.length} characters`);
    }

    return NextResponse.json(
      {
        message: "Password has been reset successfully. You can now log in with your new password.",
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Reset password error:", error);
    
    // Handle JWT signing errors specifically
    if (error instanceof Error && error.message.includes("secretOrPrivateKey")) {
      return NextResponse.json(
        {
          message: "Server configuration error. Please contact support.",
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: "An unexpected error occurred. Please try again later.",
      },
      { status: 500 }
    );
  }
}
