import {
  ExtendedApplicationStep,
  CaseFormData,
  ChecklistProgress,
} from "../components/immigration/types/workflow.types";

export const mock12StepWorkflow: ExtendedApplicationStep[] = [
  {
    id: 1,
    title: "Personal Information Collection",
    description: "Gather basic personal details and contact information",
    phase: "creation",
    status: "completed",
    stepType: "user_action",
    estimatedDuration: "15 minutes",
    details: {},
  },
  {
    id: 2,
    title: "Visa Details Specification",
    description: "Define visa type and specific requirements",
    phase: "creation",
    status: "completed",
    stepType: "user_action",
    estimatedDuration: "10 minutes",
    details: {},
  },
  {
    id: 3,
    title: "Additional Information",
    description: "Provide supplementary details and preferences",
    phase: "creation",
    status: "in_progress",
    stepType: "user_action",
    estimatedDuration: "20 minutes",
    details: {},
  },
  {
    id: 4,
    title: "Document Collection",
    description: "Upload required documents and supporting materials",
    phase: "creation",
    status: "pending",
    stepType: "user_action",
    estimatedDuration: "30 minutes",
    details: {},
  },
  {
    id: 5,
    title: "Application Review",
    description: "Review and validate all provided information",
    phase: "creation",
    status: "pending",
    stepType: "user_action",
    estimatedDuration: "15 minutes",
    details: {},
  },
  {
    id: 6,
    title: "Client Onboarding",
    description: "Initial consultation and case setup",
    phase: "processing",
    status: "pending",
    stepType: "professional_action",
    estimatedDuration: "1 hour",
    details: {},
  },
  {
    id: 7,
    title: "Checkpoint Call",
    description: "Review case details with immigration professional",
    phase: "processing",
    status: "pending",
    stepType: "professional_action",
    estimatedDuration: "45 minutes",
    details: {},
  },
  {
    id: 8,
    title: "Document Review",
    description: "Professional review of submitted documents",
    phase: "processing",
    status: "pending",
    stepType: "professional_action",
    estimatedDuration: "2 hours",
    details: {},
  },
  {
    id: 9,
    title: "Application Filing",
    description: "Submit application to relevant authorities",
    phase: "application",
    status: "pending",
    stepType: "professional_action",
    estimatedDuration: "1 hour",
    details: {},
  },
  {
    id: 10,
    title: "Application Submission",
    description: "Final submission and confirmation",
    phase: "application",
    status: "pending",
    stepType: "system_action",
    estimatedDuration: "30 minutes",
    details: {},
  },
  {
    id: 11,
    title: "Timeline Management",
    description: "Track application progress and milestones",
    phase: "timeline",
    status: "pending",
    stepType: "system_action",
    estimatedDuration: "Ongoing",
    details: {},
  },
  {
    id: 12,
    title: "Decision Processing",
    description: "Handle final decision and next steps",
    phase: "decision",
    status: "pending",
    stepType: "external_action",
    estimatedDuration: "Variable",
    details: {},
  },
];

export const mockCaseFormData: CaseFormData = {
  personalInfo: {
    surname: "",
    forename: "",
    otherName: "",
    dateOfBirth: "",
    gender: "Male",
    countryOfBirth: "",
    currentLocation: "",
    address: {
      line1: "",
      line2: "",
      line3: "",
      line4: "",
    },
    contactPhone: "",
    contactEmail: "",
  },
  visaDetails: {
    countryOfNationality: "",
    reasonForTravel: "",
    visaType: "Short Stay (C)",
    journeyType: "Single",
    purposeOfTravel: "",
    passportType: "",
    passportNumber: "",
    issuingAuthority: "",
    dateOfIssue: "",
    dateOfExpiry: "",
    proposedDates: {
      from: "",
      to: "",
    },
  },
  additionalInfo: {
    previousApplications: false,
    refusedVisa: false,
    criminalConvictions: false,
    medicalConditions: false,
    additionalDetails: "",
  },
  documents: [],
  reviewData: {
    completionPercentage: 0,
    criticalItemsComplete: false,
    recommendedItemsComplete: false,
    readyForSubmission: false,
  },
};

export const mockChecklistProgress: ChecklistProgress = {
  critical: [],
  recommended: [],
  optional: [],
  enhancement: [],
  completionPercentage: 0,
};
